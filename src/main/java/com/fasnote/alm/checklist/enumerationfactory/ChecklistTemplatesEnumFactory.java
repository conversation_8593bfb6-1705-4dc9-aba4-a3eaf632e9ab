package com.fasnote.alm.checklist.enumerationfactory;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

import org.jetbrains.annotations.NotNull;

import com.fasnote.alm.checklist.config.WebConfig;
import com.fasnote.alm.checklist.service.ChecklistTemplateService;
import com.polarion.alm.tracker.model.ITimePoint;
import com.polarion.platform.persistence.IEnumOption;
import com.polarion.platform.persistence.IEnumeration;
import com.polarion.platform.persistence.model.IPObject;
import com.polarion.platform.persistence.spi.AbstractObjectEnumFactory;
import com.polarion.platform.persistence.spi.AbstractObjectEnumeration;
import com.polarion.platform.persistence.spi.EnumOption;
import com.polarion.subterra.base.data.identification.IContextId;

public class ChecklistTemplatesEnumFactory extends AbstractObjectEnumFactory {

    @Override
    public String getName() {
        return "Checklist 模版";
    }

    @Override
    public String getOptionalFieldName() {
        return "type";
    }

    @Override
    public IEnumeration getEnumeration(final String enumId, final IContextId contextId) {
        final String query = extractValueFromEnumId(enumId);
        return new AbstractObjectEnumeration(enumId) {

            @Override
            public IEnumOption wrapOption(String optionId) {
                LinkedHashMap<String, ITimePoint> timePoints = getTimePoints(query, contextId);
                ITimePoint timePoint = timePoints.get(optionId);
                if (timePoint == null) {
                    return createPhantomOption(enumId, optionId);
                }
                return wrapTimePoint(enumId, timePoint);
            }

            @Override
            public List getAvailableOptions(Object controlValue, IEnumOption currentValue) {
                List<IEnumOption> options = new ArrayList<IEnumOption>();
                LinkedHashMap<String, ITimePoint> timePoints = getTimePoints(query, contextId);
                boolean currentPresent = false;
                for (ITimePoint timePoint : timePoints.values()) {
                    if (currentValue != null && timePoint.getId().equals(currentValue.getId())) {
                        currentPresent = true;
                    }
                    options.add(wrapTimePoint(enumId, timePoint));
                }
                if (!currentPresent && currentValue != null) {
                    options.add(currentValue);
                }
                return options;
            }

            @Override
            @NotNull
            public IEnumOption wrapObject(@NotNull IPObject object) {
                if (object instanceof ITimePoint) {
                    ITimePoint timePoint = (ITimePoint) object;
                    return wrapTimePoint(enumId, timePoint);
                }
                throw new IllegalArgumentException();
            }

        };
    }

    protected LinkedHashMap<String, ITimePoint> getTimePoints(String query, IContextId contextId) {
        ChecklistTemplateService bean = WebConfig.getSpringContext().getBean(ChecklistTemplateService.class);
        // bean.getLatestTemplateByType(query);
         return null;
    }

    protected IEnumOption wrapTimePoint(String enumId, ITimePoint timePoint) {
        return new EnumOption(enumId, timePoint.getId(), timePoint.getName() + " (" + timePoint.getTime() + ")", 0, false, getExtendedProperties(timePoint, null, null));
    }

}
